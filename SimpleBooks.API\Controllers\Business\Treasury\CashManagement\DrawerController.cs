﻿namespace SimpleBooks.API.Controllers.Business.Treasury.CashManagement
{
    public class DrawerController : BaseBusinessController<DrawerModel, IndexDrawerViewModel, CreateDrawerViewModel, UpdateDrawerViewModel>
    {
        private readonly IDrawerService _drawerService;

        public DrawerController(IDrawerService drawerService) : base(drawerService)
        {
            _drawerService = drawerService;
        }
    }
}
