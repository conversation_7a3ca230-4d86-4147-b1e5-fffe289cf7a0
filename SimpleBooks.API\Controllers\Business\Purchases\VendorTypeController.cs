﻿namespace SimpleBooks.API.Controllers.Business.Purchases
{
    public class VendorTypeController : BaseBusinessController<VendorTypeModel, IndexVendorTypeViewModel, CreateVendorTypeViewModel, UpdateVendorTypeViewModel>
    {
        private readonly IVendorTypeService _vendorTypeService;

        public VendorTypeController(IVendorTypeService vendorTypeService) : base(vendorTypeService)
        {
            _vendorTypeService = vendorTypeService;
        }
    }
}
