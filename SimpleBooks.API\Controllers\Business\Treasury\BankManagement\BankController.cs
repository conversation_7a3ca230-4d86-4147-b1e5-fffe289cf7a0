﻿namespace SimpleBooks.API.Controllers.Business.Treasury.BankManagement
{
    public class BankController : BaseBusinessController<BankModel, IndexBankViewModel, CreateBankViewModel, UpdateBankViewModel>
    {
        private readonly IBankService _bankService;

        public BankController(IBankService bankService) : base(bankService)
        {
            _bankService = bankService;
        }
    }
}
