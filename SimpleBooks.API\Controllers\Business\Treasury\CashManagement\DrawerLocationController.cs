﻿namespace SimpleBooks.API.Controllers.Business.Treasury.CashManagement
{
    public class DrawerLocationController : BaseBusinessController<DrawerLocationModel, IndexDrawerLocationViewModel, CreateDrawerLocationViewModel, UpdateDrawerLocationViewModel>
    {
        private readonly IDrawerLocationService _drawerLocationService;

        public DrawerLocationController(IDrawerLocationService drawerLocationService) : base(drawerLocationService)
        {
            _drawerLocationService = drawerLocationService;
        }
    }
}
