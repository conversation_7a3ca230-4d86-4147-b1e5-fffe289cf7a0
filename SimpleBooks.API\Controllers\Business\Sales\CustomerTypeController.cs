﻿namespace SimpleBooks.API.Controllers.Business.Sales
{
    public class CustomerTypeController : BaseBusinessController<CustomerTypeModel, IndexCustomerTypeViewModel, CreateCustomerTypeViewModel, UpdateCustomerTypeViewModel>
    {
        private readonly ICustomerTypeService _customerTypeService;

        public CustomerTypeController(ICustomerTypeService customerTypeService) : base(customerTypeService)
        {
            _customerTypeService = customerTypeService;
        }
    }
}
