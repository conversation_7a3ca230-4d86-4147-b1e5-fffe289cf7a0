﻿namespace SimpleBooks.API.Controllers.Business.Warehouse
{
    public class StoreController : BaseBusinessController<StoreModel, IndexStoreViewModel, CreateStoreViewModel, UpdateStoreViewModel>
    {
        private readonly IStoreService _storeService;

        public StoreController(IStoreService storeService) : base(storeService)
        {
            _storeService = storeService;
        }
    }
}
