﻿namespace SimpleBooks.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class BaseBusinessController<TEntity, TEntityView, TEntityCreate, TEntityUpdate> : ControllerBase
        where TEntity : class, IBaseIdentityModel
        where TEntityView : class, IBaseIdentityModel
        where TEntityCreate : BaseCreateViewModel, IEntityMapper<TEntity, TEntityCreate>
        where TEntityUpdate : BaseUpdateViewModel, IEntityMapper<TEntity, TEntityUpdate>
    {
        protected readonly ISimpleBooksBaseService<TEntity, TEntityView, TEntityCreate, TEntityUpdate> _simpleBooksBaseService;

        public BaseBusinessController(ISimpleBooksBaseService<TEntity, TEntityView, TEntityCreate, TEntityUpdate> SimpleBooksBaseService)
        {
            _simpleBooksBaseService = SimpleBooksBaseService;
        }

        [HttpGet("GetAllAsync")]
        public async Task<IActionResult> GetAllAsync()
        {
            try
            {
                var result = await _simpleBooksBaseService.GetAllAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetAllPaginationListAsync")]
        public async Task<IActionResult> GetAllPaginationListAsync(int pageNumber, int pageSize = 25)
        {
            try
            {
                if (pageNumber < 1 || pageSize < 1)
                    return BadRequest(new { Message = "PageNumber and PageSize must be greater than 0." });

                var result = await _simpleBooksBaseService.GetAllPaginationListAsync(pageNumber, pageSize);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetAllByNameAsync")]
        public async Task<IActionResult> GetAllByNameAsync(string name, int pageNumber, int pageSize = 25)
        {
            try
            {
                if (string.IsNullOrEmpty(name))
                    return BadRequest(new { Message = "Name must be filled." });
                if (pageNumber < 1 || pageSize < 1)
                    return BadRequest(new { Message = "PageNumber and PageSize must be greater than 0." });

                var result = await _simpleBooksBaseService.GetAllByNameAsync(name, pageNumber, pageSize);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetAllViewAsync")]
        public async Task<IActionResult> GetAllViewAsync(int pageNumber, int pageSize = 25)
        {
            try
            {
                if (pageNumber < 1 || pageSize < 1)
                    return BadRequest(new { Message = "PageNumber and PageSize must be greater than 0." });

                var result = await _simpleBooksBaseService.GetAllViewAsync(pageNumber, pageSize);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetAllViewByAsync")]
        public async Task<IActionResult> GetAllViewByAsync(TEntityView searchValues, int pageNumber, int pageSize = 25)
        {
            try
            {
                if (searchValues is null)
                    return BadRequest(new { Message = "Search Values must be filled." });
                if (pageNumber < 1 || pageSize < 1)
                    return BadRequest(new { Message = "PageNumber and PageSize must be greater than 0." });

                var result = await _simpleBooksBaseService.GetAllViewByAsync(searchValues, pageNumber, pageSize);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetAllViewByNameAsync")]
        public async Task<IActionResult> GetAllViewByNameAsync(string name, int pageNumber, int pageSize = 25)
        {
            try
            {
                if (string.IsNullOrEmpty(name))
                    return BadRequest(new { Message = "Name must be filled." });
                if (pageNumber < 1 || pageSize < 1)
                    return BadRequest(new { Message = "PageNumber and PageSize must be greater than 0." });

                var result = await _simpleBooksBaseService.GetAllViewByNameAsync(name, pageNumber, pageSize);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetSelectListAsync")]
        public async Task<IActionResult> GetSelectListAsync(Expression<Func<TEntity, bool>>? searchValue = null)
        {
            try
            {
                var result = await _simpleBooksBaseService.GetSelectListAsync(searchValue);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetByIdAsync")]
        public async Task<IActionResult> GetByIdAsync(string id)
        {
            try
            {
                Ulid ulid = Ulid.Parse(id);
                var result = await _simpleBooksBaseService.GetByIdAsync(ulid);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("IsExistAsync")]
        public async Task<IActionResult> IsExistAsync([FromRoute] string id)
        {
            try
            {
                Ulid ulid = Ulid.Parse(id);
                var result = await _simpleBooksBaseService.IsExistAsync(ulid);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPost("AddAsync")]
        public async Task<IActionResult> AddAsync([FromBody] TEntityCreate model)
        {
            try
            {
                if (!ModelState.IsValid)
                    return BadRequest(new { Message = "Invalid data submitted.", ModelState = ModelState });

                var result = await _simpleBooksBaseService.AddAsync(model);

                if (result is null)
                    return BadRequest(new { Message = "Failed to create entity. Please check your data and try again." });

                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPut("UpdateAsync")]
        public async Task<IActionResult> UpdateAsync([FromBody] TEntityUpdate model)
        {
            try
            {
                if (!ModelState.IsValid)
                    return BadRequest(new { Message = "Invalid data submitted.", ModelState = ModelState });

                var result = await _simpleBooksBaseService.UpdateAsync(model);

                if (result is null)
                    return BadRequest(new { Message = "Failed to update entity. Please check your data and try again." });

                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpDelete("RemoveAsync/{id}")]
        public async Task<IActionResult> RemoveAsync([FromRoute] string id)
        {
            try
            {
                Ulid ulid = Ulid.Parse(id);
                var result = await _simpleBooksBaseService.RemoveAsync(ulid);

                if (!result)
                    return BadRequest(new { Message = "Failed to delete entity. It may not exist." });

                return Ok(new { Message = "Entity deleted successfully." });
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
