﻿namespace SimpleBooks.API.Controllers.Business.Treasury
{
    public class ExpensesController : BaseBusinessController<ExpensesModel, IndexExpensesViewModel, CreateExpensesViewModel, UpdateExpensesViewModel>
    {
        private readonly IExpensesService _expensesService;

        public ExpensesController(IExpensesService expensesService) : base(expensesService)
        {
            _expensesService = expensesService;
        }
    }
}
