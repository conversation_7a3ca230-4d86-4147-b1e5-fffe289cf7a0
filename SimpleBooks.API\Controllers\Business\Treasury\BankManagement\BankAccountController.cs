﻿namespace SimpleBooks.API.Controllers.Business.Treasury.BankManagement
{
    public class BankAccountController : BaseBusinessController<BankAccountModel, IndexBankAccountViewModel, CreateBankAccountViewModel, UpdateBankAccountViewModel>
    {
        private readonly IBankAccountService _bankAccountService;

        public BankAccountController(IBankAccountService bankAccountService) : base(bankAccountService)
        {
            _bankAccountService = bankAccountService;
        }
    }
}
