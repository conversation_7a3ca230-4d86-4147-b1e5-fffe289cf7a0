﻿namespace SimpleBooks.API.Controllers.Business.HR
{
    public class EmployeeController : BaseBusinessController<EmployeeModel, IndexEmployeeViewModel, CreateEmployeeViewModel, UpdateEmployeeViewModel>
    {
        private readonly IEmployeeService _employeeService;

        public EmployeeController(IEmployeeService employeeService) : base(employeeService)
        {
            _employeeService = employeeService;
        }

        [HttpGet(nameof(GetRepEmployeeSelectListAsync))]
        public async Task<IActionResult> GetRepEmployeeSelectListAsync()
        {
            try
            {
                var result = await _employeeService.GetRepEmployeeSelectListAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
