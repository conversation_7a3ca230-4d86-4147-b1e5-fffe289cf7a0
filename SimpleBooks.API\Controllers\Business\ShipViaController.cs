﻿namespace SimpleBooks.API.Controllers.Business
{
    public class ShipViaController : BaseBusinessController<ShipViaModel, IndexShipViaViewModel, CreateShipViaViewModel, UpdateShipViaViewModel>
    {
        private readonly IShipViaService _shipViaService;

        public ShipViaController(IShipViaService shipViaService) : base(shipViaService)
        {
            _shipViaService = shipViaService;
        }
    }
}
