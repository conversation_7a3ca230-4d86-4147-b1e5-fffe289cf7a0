﻿namespace GMCadiomCore.Models.ViewModel
{
    public class BaseIndexViewModel<T> : BaseIndexViewModel
    {
        public PaginationList<T> MainList { get; set; }
    }

    public class BaseIndexViewModel
    {
        public string SearchValue { get; set; }
        public int PageNumber { get; set; }
        public virtual bool AllowCreateNew { get; set; } = true;
        public virtual bool AllowSearch { get; set; } = true;
        public virtual bool AllowExcelExport { get; set; } = true;
        public virtual bool AllowExcelImport { get; set; } = true;
        public virtual bool AllowPdfExport { get; set; } = true;
    }
}
