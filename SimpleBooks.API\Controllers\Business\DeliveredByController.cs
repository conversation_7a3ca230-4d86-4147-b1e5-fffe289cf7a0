﻿namespace SimpleBooks.API.Controllers.Business
{
    public class DeliveredByController : BaseBusinessController<DeliveredByModel, IndexDeliveredByViewModel, CreateDeliveredByViewModel, UpdateDeliveredByViewModel>
    {
        private readonly IDeliveredByService _deliveredByService;

        public DeliveredByController(IDeliveredByService deliveredByService) : base(deliveredByService)
        {
            _deliveredByService = deliveredByService;
        }
    }
}
