﻿namespace SimpleBooks.API.Controllers.Business.Warehouse
{
    public class ProductCategoryController : BaseBusinessController<ProductCategoryModel, IndexProductCategoryViewModel, CreateProductCategoryViewModel, UpdateProductCategoryViewModel>
    {
        private readonly IProductCategoryService _productCategoryService;

        public ProductCategoryController(IProductCategoryService productCategoryService) : base(productCategoryService)
        {
            _productCategoryService = productCategoryService;
        }
    }
}
